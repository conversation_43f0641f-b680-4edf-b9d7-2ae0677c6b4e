import { redirect } from "next/navigation"
import { getSupabaseAdmin } from "@/lib/supabase"

interface SlugPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function SlugPage({ params }: SlugPageProps) {
  const { slug } = await params

  // Skip processing for common static files that shouldn't be handled as slugs
  const staticFiles = ['favicon.ico', 'robots.txt', 'sitemap.xml', 'manifest.json']
  if (staticFiles.includes(slug)) {
    redirect("/events")
  }

  // Skip processing for file extensions (e.g., .js, .css, .png, etc.)
  if (slug.includes('.')) {
    redirect("/events")
  }

  // Validate that this is a valid event slug by checking if it exists in the database
  try {
    const supabaseAdmin = getSupabaseAdmin()
    const { data, error } = await supabaseAdmin
      .from("events")
      .select("slug")
      .eq("slug", slug)
      .eq("is_published", true) // Only redirect to published events
      .single()

    if (data && !error) {
      // Valid event slug found, redirect to the full events path
      redirect(`/events/${slug}`)
    }
  } catch (error) {
    // Error checking event slug - redirect to events page
  }

  // If we reach here, the slug doesn't exist or isn't published
  // Redirect to 404 or events page
  redirect("/events")
}

// Generate metadata for the page
export async function generateMetadata({ params }: SlugPageProps) {
  const { slug } = await params

  try {
    const supabaseAdmin = getSupabaseAdmin()
    const { data, error } = await supabaseAdmin
      .from("events")
      .select("title, description")
      .eq("slug", slug)
      .eq("is_published", true)
      .single()

    if (data && !error) {
      return {
        title: `${data.title} | mTicketz`,
        description: data.description || `Join ${data.title} - Event details and registration`,
      }
    }
  } catch (error) {
    console.error("Error generating metadata for slug:", error)
  }

  return {
    title: "Event | mTicketz",
    description: "Event details and registration",
  }
}
